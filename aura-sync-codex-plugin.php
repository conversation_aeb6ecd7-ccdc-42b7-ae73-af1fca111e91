<?php
/**
 * Plugin Name:         aura Sync Plugin
 * Description:         Manages Activities, Guides, and syncs data with the aura app's Firebase database.
 * Version:             5.0.0
 * Author:              Monoloci Development Team
 * Author URI:          https://monoloci.app
 * License:             GPL v2 or later
 * License URI:         https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:         aura-sync
 */

if ( ! defined( 'ABSPATH' ) ) exit;


// Autoloader for Firebase SDK
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

use Kreait\Firebase\Factory;
use Kreait\Firebase\Exception\FirebaseException;

class aura_Sync_Plugin {

    private $firestore;

    public function __construct() {
        // Backend hooks
        add_action( 'init', [ $this, 'register_post_types' ] );
        add_action( 'init', [ $this, 'register_taxonomies' ], 0 );
        add_action( 'add_meta_boxes', [ $this, 'register_meta_boxes' ] );
        add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_admin_scripts' ] );
        add_action( 'save_post_activity', [ $this, 'save_and_sync_activity' ], 10, 2 );
        add_action( 'save_post_directory', [ $this, 'save_and_sync_directory_item' ], 10, 2 );
        add_action( 'save_post_codex', [ $this, 'save_and_sync_codex' ], 10, 2 );
        add_action( 'save_post', [ $this, 'save_activity_meta_box_data' ] );
        add_action( 'delete_post', [ $this, 'delete_activity_from_firebase' ], 10, 1 );

        // Frontend Form hooks
        add_action('init', [ $this, 'handle_frontend_form_submission' ]);
        add_action('init', [ $this, 'handle_frontend_deletion' ]);
        add_shortcode('aura_activity_form', [ $this, 'render_frontend_form' ]);
        add_shortcode('aura_operator_dashboard', [ $this, 'render_operator_dashboard' ]);
        add_action('wp_enqueue_scripts', [ $this, 'enqueue_frontend_scripts' ]);
        
        // Operator Permissions & Redirects
        add_action( 'admin_init', [ $this, 'redirect_non_admins_from_backend' ], 1 );
        add_action( 'admin_menu', [ $this, 'remove_admin_menus_for_operators' ], 999 );
        add_action( 'pre_get_posts', [ $this, 'restrict_media_library_to_own_uploads' ] );
        
        // Frontend Firebase Configuration
        add_action( 'wp_head', [ $this, 'inject_firebase_config' ] );
    }
    
    /**
     * Hides all non-essential admin menu items for the Operator role.
     * <<< FINAL VERSION >>>
     */
    public function remove_admin_menus_for_operators() {
        if ( in_array('um_operator', (array) wp_get_current_user()->roles) ) {
            remove_menu_page( 'index.php' );                  // Dashboard
            remove_menu_page( 'edit.php' );                   // Posts
            remove_menu_page( 'edit.php?post_type=page' );    // Pages
            remove_menu_page( 'edit.php?post_type=activity' ); // Activities
            remove_menu_page( 'edit-comments.php' );          // Comments
            remove_menu_page( 'themes.php' );                 // Appearance
            remove_menu_page( 'plugins.php' );                // Plugins
            remove_menu_page( 'users.php' );                  // Users
            remove_menu_page( 'tools.php' );                  // Tools
            remove_menu_page( 'options-general.php' );        // Settings
            remove_menu_page( 'ultimatemember' );             // Ultimate Member
            remove_menu_page( 'profile.php' );                // Profile
            remove_menu_page( 'wpcf7' );                      // Contact Form 7
            remove_menu_page( 'edit.php?post_type=elementor_library' ); // Elementor Templates
        }
    }

    /**
     * Allows non-admins access to the Media Library ONLY.
     */
    public function redirect_non_admins_from_backend() {
        if ( is_admin() && ! defined('DOING_AJAX') ) {
            if ( current_user_can('manage_options') ) {
                return;
            }
            if ( in_array('um_operator', (array) wp_get_current_user()->roles) ) {
                global $pagenow;
                $allowed_pages = ['upload.php', 'media.php', 'media-new.php'];
                if ( !in_array($pagenow, $allowed_pages) ) {
                    wp_redirect( home_url('/operator-dashboard/') );
                    exit;
                }
            }
        }
    }
    
    /**
     * Restricts media library view to own uploads for non-admins.
     * <<< FINAL VERSION >>>
     */
    public function restrict_media_library_to_own_uploads( $query ) {
        // We only affect queries in the admin area
        if ( !is_admin() ) {
            return;
        }
        // We only affect users with the 'um_operator' role
        if ( !in_array('um_operator', (array) wp_get_current_user()->roles) ) {
            return;
        }
        // This targets the main grid view AJAX query AND the list view main query
        if ( isset($query->query['post_type']) && $query->query['post_type'] === 'attachment' ) {
             $query->set('author', get_current_user_id());
        }
    }

    /**
     * Renders the operator dashboard with all final changes.
     * <<< FINAL VERSION >>>
     */
    public function render_operator_dashboard() {
        if (!is_user_logged_in()) { return '<p>You must be logged in to view your dashboard. Please <a href="' . wp_login_url(get_permalink()) . '">log in</a>.</p>'; }
        ob_start();
        $current_user_id = get_current_user_id();
        $args = [ 'post_type' => 'activity', 'author' => $current_user_id, 'posts_per_page' => -1, 'post_status' => ['publish', 'pending', 'draft'] ];
        $activity_query = new WP_Query($args);
        if ($activity_query->have_posts()) : ?>
            <style>.aura-dashboard-table { width: 100%; border-collapse: collapse; margin-top: 20px; } .aura-dashboard-table th, .aura-dashboard-table td { padding: 12px; border: 1px solid #ddd; text-align: left; } .aura-dashboard-table th { background-color: #f7f7f7; } .status-pending { font-style: italic; color: #777; } .status-publish { font-weight: bold; color: #2e7d32; } .aura-actions a { margin-right: 10px; }</style>
            <table class="aura-dashboard-table">
                <thead><tr><th>Activity Code</th><th>Activity Title</th><th>Date</th><th>Status</th><th>Actions</th></tr></thead>
                <tbody>
                    <?php while ($activity_query->have_posts()) : $activity_query->the_post(); ?>
                        <?php
                            $post_id = get_the_ID();
                            $activity_code = get_post_meta($post_id, '_aura_activity_code', true);
                            $activity_date = get_post_meta($post_id, '_aura_activity_date', true);
                            $activity_time = get_post_meta($post_id, '_aura_activity_time', true);
                            $full_date = $activity_time ? $activity_date . ' at ' . $activity_time : $activity_date;
                            $status = get_post_status();
                            $delete_link = wp_nonce_url( add_query_arg(['action' => 'aura_delete_activity', 'post_id' => $post_id]), 'aura_delete_activity_' . $post_id );
                            $edit_page_url = get_permalink(get_page_by_path('edit-activity'));
                            $edit_link = add_query_arg('activity_id', $post_id, $edit_page_url);
                        ?>
                        <tr>
                            <td><?php echo esc_html($activity_code); ?></td>
                            <td><?php the_title(); ?></td>
                            <td><?php echo esc_html($full_date); ?></td>
                            <td class="status-<?php echo esc_attr($status); ?>"><?php echo esc_html(ucfirst($status)); ?></td>
                            <td class="aura-actions">
                                <a href="<?php echo esc_url($edit_link); ?>">Edit</a>
                                <a href="<?php echo esc_url($delete_link); ?>" onclick="return confirm('Are you sure you want to permanently delete this activity?');" style="color:#a00;">Delete</a>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        <?php else : ?><p>You have not submitted any activities yet.</p><?php endif; 
        wp_reset_postdata();
        return ob_get_clean();
    }
    
    /**
     * Registers the Custom Post Types.
     * <<< FINAL VERSION >>>
     */
    public function register_post_types() {
        $activity_labels = [ 'name' => 'Activities', 'singular_name' => 'Activity' ];
        $activity_args = [
            'label' => __( 'Activity', 'aura-sync' ),
            'labels' => $activity_labels,
            'supports' => [ 'title', 'editor', 'thumbnail', 'author' ],
            'hierarchical' => false,
            'public' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'menu_position' => 5,
            'menu_icon' => 'dashicons-calendar-alt',
            'can_export' => true,
            'has_archive' => true,
            'publicly_queryable' => false, // Set to false to prevent access to single post pages
            'capability_type' => 'post',
            'show_in_rest' => true,
        ];
        register_post_type( 'activity', $activity_args );

        $directory_labels = [ 'name' => 'Directory', 'singular_name' => 'Directory Item' ];
        $directory_args = [
            'label' => __( 'Directory', 'aura-sync' ),
            'labels' => $directory_labels,
            'supports' => [ 'title', 'editor', 'thumbnail', 'author' ],
            'hierarchical' => false,
            'public' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'menu_position' => 6,
            'menu_icon' => 'dashicons-location-alt',
            'can_export' => true,
            'has_archive' => true,
            'publicly_queryable' => false,
            'capability_type' => 'post',
            'show_in_rest' => true,
        ];
        register_post_type( 'directory', $directory_args );

        // The 'codex' post type is now registered via the CPT UI plugin.
        // The registration code has been removed from this file to prevent conflicts.
    }

    // All other functions from here are unchanged and included for completeness.
    public function handle_frontend_form_submission() { if ( ! isset($_POST['aura_form_submit_nonce']) || ! wp_verify_nonce($_POST['aura_form_submit_nonce'], 'aura_form_submit_action') ) { return; } $post_id = (isset($_POST['activity_id'])) ? absint($_POST['activity_id']) : 0; $is_update = ($post_id > 0); if ($is_update && !current_user_can('edit_post', $post_id)) { wp_die('You do not have permission to edit this activity.'); } $activity_title = sanitize_text_field($_POST['activity_title']); $activity_description = wp_kses_post($_POST['activity_description']); $activity_date = sanitize_text_field($_POST['activity_date']); $activity_time = sanitize_text_field($_POST['activity_time']); if (empty($activity_title)) { wp_die('Please provide an activity title.'); } $post_data = [ 'post_title' => $activity_title, 'post_content' => $activity_description, 'post_type' => 'activity', 'post_status' => 'publish', 'post_author' => get_current_user_id() ]; if ($is_update) { $post_data['ID'] = $post_id; wp_update_post($post_data, true); } else { $post_id = wp_insert_post($post_data, true); } if ($post_id && !is_wp_error($post_id)) { update_post_meta($post_id, '_aura_activity_date', $activity_date); update_post_meta($post_id, '_aura_activity_time', $activity_time); update_post_meta($post_id, '_aura_activity_description', $activity_description); if ( ! empty($_FILES['featured_image']['name']) ) { require_once(ABSPATH . 'wp-admin/includes/image.php'); require_once(ABSPATH . 'wp-admin/includes/file.php'); require_once(ABSPATH . 'wp-admin/includes/media.php'); $attachment_id = media_handle_upload('featured_image', $post_id); if (!is_wp_error($attachment_id)) { set_post_thumbnail($post_id, $attachment_id); } } if ( ! empty($_FILES['activity_documents']['name'][0]) ) { require_once(ABSPATH . 'wp-admin/includes/image.php'); require_once(ABSPATH . 'wp-admin/includes/file.php'); require_once(ABSPATH . 'wp-admin/includes/media.php'); $attachment_ids = []; $files = $_FILES['activity_documents']; foreach ($files['name'] as $key => $value) { if ($files['name'][$key]) { $file = [ 'name' => $files['name'][$key], 'type' => $files['type'][$key], 'tmp_name' => $files['tmp_name'][$key], 'error' => $files['error'][$key], 'size' => $files['size'][$key] ]; $movefile = wp_handle_upload($file, ['test_form' => false]); if ($movefile && !isset($movefile['error'])) { $attachment = [ 'guid' => $movefile['url'], 'post_mime_type' => $movefile['type'], 'post_title' => preg_replace('/\.[^.]+$/', '', basename($file['name'])), 'post_content' => '', 'post_status' => 'inherit' ]; $attach_id = wp_insert_attachment($attachment, $movefile['file'], $post_id); $attach_data = wp_generate_attachment_metadata($attach_id, $movefile['file']); wp_update_attachment_metadata($attach_id, $attach_data); $attachment_ids[] = $attach_id; } } } if ( ! empty($attachment_ids) ) { update_post_meta($post_id, '_aura_activity_document_ids', implode(',', $attachment_ids)); } } $this->save_and_sync_activity($post_id, get_post($post_id)); } $redirect_url = get_permalink(get_page_by_path('operator-dashboard')); $redirect_url = add_query_arg(['activity_submitted' => 'true', 'updated' => $is_update ? 'true' : 'false'], $redirect_url); wp_redirect($redirect_url); exit; }
    public function render_frontend_form() { if (!is_user_logged_in()) { return '<p>You must be logged in to add an activity. Please <a href="' . wp_login_url(get_permalink()) . '">log in</a>.</p>'; } $post_id = 0; $is_editing = false; if (isset($_GET['activity_id']) && is_numeric($_GET['activity_id'])) { $post_id = absint($_GET['activity_id']); if (get_post_type($post_id) === 'activity' && current_user_can('edit_post', $post_id)) { $is_editing = true; $post_to_edit = get_post($post_id); } else { return '<p>You do not have permission to edit this activity.</p>'; } } $activity_title = $is_editing ? $post_to_edit->post_title : ''; $activity_description = $is_editing ? $post_to_edit->post_content : ''; $activity_date = $is_editing ? get_post_meta($post_id, '_aura_activity_date', true) : ''; $activity_time = $is_editing ? get_post_meta($post_id, '_aura_activity_time', true) : ''; ob_start(); ?> <?php if(isset($_GET['activity_submitted']) && $_GET['activity_submitted'] == 'true'): ?> <div class="aura-form-success" style="padding:15px; background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; margin-bottom: 20px;">Your activity has been <?php echo (isset($_GET['updated']) && $_GET['updated'] == 'true') ? 'updated' : 'completed'; ?>. Thank you!</div> <?php endif; ?> <form id="aura-new-activity-form" method="post" enctype="multipart/form-data"><style> #aura-new-activity-form { max-width: 600px; } .aura-form-field { margin-bottom: 20px; } .aura-form-field label { display: block; font-weight: bold; margin-bottom: 5px; } .aura-form-field input[type="text"], .aura-form-field textarea, .aura-form-field select, .aura-form-field input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; } .aura-form-field textarea { height: 150px; } .aura-form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; } .current-featured-image img { border: 2px solid #ddd; padding: 5px; max-width: 200px; margin-top: 10px; } </style><div class="aura-form-field"><label for="activity_title">Activity Title</label><input type="text" id="activity_title" name="activity_title" value="<?php echo esc_attr($activity_title); ?>" required></div><div class="aura-form-field"><label for="activity_description">Activity Description</label><textarea id="activity_description" name="activity_description" required rows="5"><?php echo esc_textarea($activity_description); ?></textarea></div><div class="aura-form-grid"><div class="aura-form-field"><label for="aura_frontend_activity_date">Activity Date</label><input type="text" id="aura_frontend_activity_date" name="activity_date" value="<?php echo esc_attr($activity_date); ?>" required autocomplete="off" placeholder="Click to select date"></div><div class="aura-form-field"><label for="activity_time">Activity Time</label><input type="text" id="activity_time" name="activity_time" value="<?php echo esc_attr($activity_time); ?>" required placeholder="e.g., 9:00 AM"></div></div><div class="aura-form-field"><label for="featured_image">Featured Image</label><input type="file" id="featured_image" name="featured_image" accept="image/*"><?php if ($is_editing && has_post_thumbnail($post_id)) { echo '<div class="current-featured-image">' . get_the_post_thumbnail($post_id, 'medium') . '<br><small>Current image. Uploading a new file will replace it.</small></div>'; } ?></div><div class="aura-form-field"><label for="activity_documents">Activity Documents (Waivers, etc.)</label><input type="file" id="activity_documents" name="activity_documents[]" multiple><?php if ($is_editing) { echo '<p><small>Note: Submitting new documents will replace all existing ones.</small></p>'; } ?></div><?php wp_nonce_field('aura_form_submit_action', 'aura_form_submit_nonce'); ?><?php if ($is_editing) : ?><input type="hidden" name="activity_id" value="<?php echo esc_attr($post_id); ?>"><?php endif; ?><div class="aura-form-field"><button type="submit"><?php echo $is_editing ? 'Update Activity' : 'Submit Activity'; ?></button></div></form><?php return ob_get_clean(); }
    public function enqueue_frontend_scripts() { global $post; if ( is_a( $post, 'WP_Post' ) && ( has_shortcode( $post->post_content, 'aura_activity_form' ) || has_shortcode( $post->post_content, 'aura_operator_dashboard' ) ) ) { wp_enqueue_script('jquery-ui-datepicker'); wp_enqueue_style('jquery-ui-style', 'https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css', true); wp_enqueue_script('aura-main-scripts', plugin_dir_url( __FILE__ ) . 'js/main-scripts.js', ['jquery', 'jquery-ui-datepicker'], '1.5.0', true); } }
    public function handle_frontend_deletion() { if ( !isset($_GET['action']) || $_GET['action'] !== 'aura_delete_activity' || !isset($_GET['post_id']) ) { return; } $post_id_to_delete = absint($_GET['post_id']); if ( !isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'aura_delete_activity_' . $post_id_to_delete) ) { wp_die('Security check failed.'); } if ( current_user_can('delete_post', $post_id_to_delete) ) { $this->delete_activity_from_firebase($post_id_to_delete); wp_delete_post($post_id_to_delete, true); wp_redirect(remove_query_arg(['action', 'post_id', '_wpnonce'], wp_get_referer())); exit; } else { wp_die('You do not have permission to delete this item.'); } }
/**
 * Saves activity meta data and syncs the complete activity data to Firebase.
 * This version writes to the correct public path readable by the app.
 *
 * @param int $post_id The ID of the post being saved.
 * @param WP_Post $post The post object.
 */
public function save_and_sync_activity( $post_id, $post ) {
    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) return;
    if ( ! current_user_can( 'edit_post', $post_id ) ) return;
    $firestore = $this->initialize_firebase();
    if ( ! $firestore ) return;
    $author_id = $post->post_author;
    $company_name = get_user_meta( $author_id, 'company', true ) ?: get_the_author_meta( 'display_name', $author_id );
    $company_logo_url = function_exists('um_get_user_avatar_url') ? um_get_user_avatar_url( $author_id ) : '';
    $activity_code = get_post_meta( $post_id, '_aura_activity_code', true );
    $date_string = get_post_meta( $post_id, '_aura_activity_date', true );
    $time_string = get_post_meta( $post_id, '_aura_activity_time', true );
    $full_date = $time_string ? $date_string . ' at ' . $time_string : $date_string;
    $document_ids_str = get_post_meta($post_id, '_aura_activity_document_ids', true);
    $documents = [];
    if (!empty($document_ids_str)) {
        foreach(explode(',', $document_ids_str) as $id) {
            if(empty(trim($id))) continue;
            $documents[] = [ 'name' => basename(get_attached_file($id)), 'url'  => wp_get_attachment_url($id) ];
        }
    }
    $data_to_sync = [
        'name' => $post->post_title, 'id' => $post->post_name, 'description' => $post->post_content,
        'date' => $full_date, 'company' => $company_name, 'company_logo' => $company_logo_url,
        'guide' => null, 'documents' => $documents,
        'featured_image' => has_post_thumbnail( $post_id ) ? get_the_post_thumbnail_url( $post_id, 'full' ) : null
    ];
    $collection_path = "artifacts/aura-global-app/public/data/activities";
    try {
        $firestore->database()->collection($collection_path)->document($activity_code)->set($data_to_sync, ['merge' => true]);
    } catch (Exception $e) {
        error_log('Firebase Sync Error for activity ' . $post_id . ': ' . $e->getMessage());
    }
}

public function save_and_sync_directory_item( $post_id, $post ) {
    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) return;
    if ( ! current_user_can( 'edit_post', $post_id ) ) return;
    $firestore = $this->initialize_firebase();
    if ( ! $firestore ) return;
    $region_terms = wp_get_post_terms( $post_id, 'region', ['fields' => 'names'] );
    $regions = !is_wp_error($region_terms) && !empty($region_terms) ? $region_terms : [];
    $data_to_sync = [
        'name' => $post->post_title, 'description' => $post->post_content, 'regions' => $regions,
        'featured_image' => has_post_thumbnail( $post_id ) ? get_the_post_thumbnail_url( $post_id, 'full' ) : null,
    ];
    $collection_path = "artifacts/aura-global-app/public/data/directory";
    try {
        $firestore->database()->collection($collection_path)->document((string)$post_id)->set($data_to_sync, ['merge' => true]);
    } catch (Exception $e) {
        error_log('Firebase Sync Error for directory item ' . $post_id . ': ' . $e->getMessage());
    }
}

public function save_and_sync_codex( $post_id, $post ) {
    error_log("Aura Sync: save_and_sync_codex hook triggered for post ID: {$post_id}");

    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) return;
    if ( ! current_user_can( 'edit_post', $post_id ) ) return;
    if ( ! function_exists( 'get_field' ) ) {
        error_log('Aura Sync CRITICAL: ACF plugin is not active.');
        return;
    }

    $firestore = $this->initialize_firebase();
    if ( ! $firestore ) {
        error_log("Aura Sync CRITICAL: Firestore could not be initialized for post ID: {$post_id}");
        return;
    }
    error_log("Aura Sync: Firebase initialized for post ID: {$post_id}");

    // Try multiple methods to get repeater data
    $nodes = null;

    // Method 1: Standard get_field with formatted = true (default)
    $nodes = get_field('node', $post_id);
    error_log("Aura Sync: Method 1 - get_field('node', {$post_id}): " . print_r($nodes, true));

    // Method 2: If Method 1 fails, try with formatted = false
    if ( empty($nodes) ) {
        $nodes = get_field('node', $post_id, false);
        error_log("Aura Sync: Method 2 - get_field('node', {$post_id}, false): " . print_r($nodes, true));
    }

    // Method 3: Try getting raw meta data
    if ( empty($nodes) ) {
        $nodes = get_post_meta($post_id, 'node', true);
        error_log("Aura Sync: Method 3 - get_post_meta({$post_id}, 'node', true): " . print_r($nodes, true));
    }

    // Method 4: Try getting all meta data to see what's available
    if ( empty($nodes) ) {
        $all_meta = get_post_meta($post_id);
        error_log("Aura Sync: Method 4 - All meta data for post {$post_id}: " . print_r($all_meta, true));

        // Look for any meta keys that might contain node data
        foreach ($all_meta as $key => $value) {
            if (strpos($key, 'node') !== false) {
                error_log("Aura Sync: Found potential node meta key: {$key} = " . print_r($value, true));
            }
        }
    }

    if ( empty($nodes) || !is_array($nodes) ) {
        error_log("Aura Sync: No node data found or data is not an array for post ID: {$post_id}. Sync will stop.");
        return;
    }

    try {
        $codex_id = $post->post_name;
        if (empty($codex_id)) {
            $codex_id = sanitize_title($post->post_title);
            error_log("Aura Sync: Using sanitized title as codex_id: {$codex_id}");
        }

        $codex_doc_ref = $firestore->database()->collection("codexes")->document($codex_id);

        $codex_doc_ref->set(['title' => $post->post_title, 'description' => $post->post_content], ['merge' => true]);
        error_log("Aura Sync: Synced main data for codex: {$codex_id}");

        $nodes_collection_ref = $codex_doc_ref->collection('nodes');
        $node_count = 0;

        foreach ( $nodes as $index => $node_row ) {
            error_log("Aura Sync: Processing node {$index}: " . print_r($node_row, true));

            // Handle different data structures
            if (!is_array($node_row)) {
                error_log("Aura Sync: Node {$index} is not an array, skipping");
                continue;
            }

            // Extract node name with fallbacks
            $node_name = '';
            if (isset($node_row['name'])) {
                $node_name = $node_row['name'];
            } elseif (isset($node_row['field_node_name'])) {
                $node_name = $node_row['field_node_name'];
            }

            if (empty($node_name)) {
                error_log("Aura Sync: Node {$index} has no name, skipping");
                continue;
            }

            $node_id = sanitize_title($node_name);

            // Build node data with proper field mapping
            $node_data = [
                'name' => $node_name,
                'glyph' => $this->get_node_field_value($node_row, ['glyph', 'field_node_glyph']),
                'tone' => $this->get_node_field_value($node_row, ['tone', 'field_node_tone']),
                'info_text' => $this->get_node_field_value($node_row, ['info_text', 'field_node_info_text']),
                'image' => $this->get_node_field_value($node_row, ['image', 'field_node_image']),
            ];

            // Handle location data
            $location = $this->get_node_field_value($node_row, ['location', 'field_node_location']);
            if (is_array($location) && isset($location['lat']) && isset($location['lng'])) {
                $node_data['location'] = new \Google\Cloud\Firestore\GeoPoint(
                    floatval($location['lat']),
                    floatval($location['lng'])
                );
            }

            error_log("Aura Sync: Syncing node data: " . print_r($node_data, true));
            $nodes_collection_ref->document($node_id)->set($node_data);
            $node_count++;
        }

        error_log("Aura Sync: Successfully processed and synced {$node_count} nodes for codex: {$codex_id}");

    } catch (Exception $e) {
        error_log("Aura Sync CRITICAL ERROR during sync for post ID {$post_id}: " . $e->getMessage());
        error_log("Aura Sync ERROR Stack trace: " . $e->getTraceAsString());
    }

    /**
     * Helper function to get node field values with fallback field names
     */
    private function get_node_field_value($node_row, $field_names) {
        foreach ($field_names as $field_name) {
            if (isset($node_row[$field_name]) && !empty($node_row[$field_name])) {
                return $node_row[$field_name];
            }
        }
        return null;
    }

    /**
     * Debug function to inspect ACF field structure
     * Add this as a shortcode [debug_acf_fields post_id="123"] to test
     */
    public function debug_acf_fields($atts) {
        if (!current_user_can('manage_options')) {
            return 'Access denied';
        }

        $atts = shortcode_atts(['post_id' => 0], $atts);
        $post_id = intval($atts['post_id']);

        if (!$post_id) {
            return 'Please provide a post_id';
        }

        ob_start();
        echo "<h3>ACF Debug for Post ID: {$post_id}</h3>";

        // Get all ACF fields
        $fields = get_fields($post_id);
        echo "<h4>All ACF Fields:</h4><pre>" . print_r($fields, true) . "</pre>";

        // Get specific node field
        $nodes = get_field('node', $post_id);
        echo "<h4>Node Field (formatted):</h4><pre>" . print_r($nodes, true) . "</pre>";

        $nodes_raw = get_field('node', $post_id, false);
        echo "<h4>Node Field (raw):</h4><pre>" . print_r($nodes_raw, true) . "</pre>";

        // Get all meta
        $all_meta = get_post_meta($post_id);
        echo "<h4>All Post Meta:</h4><pre>" . print_r($all_meta, true) . "</pre>";

        return ob_get_clean();
    }
}

private function initialize_firebase() {
        if ($this->firestore) {
            error_log('aura Sync Debug: Firestore already initialized.');
            return $this->firestore;
        }

        $autoloader = __DIR__ . '/vendor/autoload.php';
        if (file_exists($autoloader)) {
            require_once $autoloader;
            error_log('aura Sync Debug: Autoloader found and included.');
        } else {
            error_log('aura Sync Error: Autoloader not found at ' . $autoloader);
            return null;
        }

        if (!class_exists('Kreait\Firebase\Factory')) {
            error_log('aura Sync Error: Firebase SDK (Kreait\Firebase\Factory) not found even after including autoloader.');
            return null;
        }
        error_log('aura Sync Debug: Firebase SDK class exists.');

        try {
            $serviceAccountPath = __DIR__ . '/config/firebase-service-account.json';
            error_log('aura Sync Debug: Looking for service account file at: ' . $serviceAccountPath);

            if (!file_exists($serviceAccountPath)) {
                error_log('aura Sync Error: Firebase service account file not found at the specified path.');
                return null;
            }
            error_log('aura Sync Debug: Service account file found.');
            
            $factory = (new Factory)->withServiceAccount($serviceAccountPath);
            $this->firestore = $factory->createFirestore();
            error_log('aura Sync Debug: Firestore initialized successfully.');
            return $this->firestore;
        } catch (FirebaseException | Exception $e) {
            error_log('aura Sync Error: Firebase initialization failed. Message: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Helper function to get node field values with fallback field names
     */
    private function get_node_field_value($node_row, $field_names) {
        foreach ($field_names as $field_name) {
            if (isset($node_row[$field_name]) && !empty($node_row[$field_name])) {
                return $node_row[$field_name];
            }
        }
        return null;
    }

    /**
     * Helper function to get node field values with fallback field names
     */
    private function get_node_field_value($node_row, $field_names) {
        foreach ($field_names as $field_name) {
            if (isset($node_row[$field_name]) && !empty($node_row[$field_name])) {
                return $node_row[$field_name];
            }
        }
        return null;
    }

    public function delete_activity_from_firebase($post_id) {
        if (get_post_type($post_id) !== 'activity') return;
        
        $firestore = $this->initialize_firebase();
        if (!$firestore) return;
        
        $activity_code = get_post_meta($post_id, '_aura_activity_code', true);
        if (empty($activity_code)) return;
        
        // Use the same collection path as in save_and_sync_activity
        $collection_path = "artifacts/aura-global-app/public/data/activities";
        
        try {
            $firestore->database()
                     ->collection($collection_path)
                     ->document($activity_code)
                     ->delete();
        } catch (FirebaseException | Exception $e) {
            error_log('Firebase Delete Error for post ' . $post_id . ': ' . $e->getMessage());
        }
    }
    public function enqueue_admin_scripts( $hook ) { if ( 'post.php' != $hook && 'post-new.php' != $hook ) { return; } if ( 'activity' !== get_post_type() ) { return; } wp_enqueue_script('jquery-ui-datepicker'); wp_enqueue_style('jquery-ui-style', 'https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css', true); wp_enqueue_media(); wp_enqueue_script('aura-main-scripts', plugin_dir_url( __FILE__ ) . 'js/main-scripts.js', ['jquery', 'jquery-ui-datepicker'], '1.5.0', true); }
    public function register_taxonomies() {
        $company_labels = [ 'name' => 'Companies', 'singular_name' => 'Company' ];
        $company_args = [ 'hierarchical' => true, 'labels' => $company_labels, 'show_ui' => true, 'show_admin_column' => true, 'query_var' => true, 'rewrite' => [ 'slug' => 'company' ], 'show_in_rest' => true, ];
        register_taxonomy( 'company', [ 'activity' ], $company_args );

        $region_labels = [ 'name' => 'Regions', 'singular_name' => 'Region' ];
        $region_args = [
            'hierarchical' => true,
            'labels' => $region_labels,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => [ 'slug' => 'region' ],
            'show_in_rest' => true,
        ];
        register_taxonomy( 'region', [ 'directory' ], $region_args );
    }
    public function register_meta_boxes() { add_meta_box('aura_activity_details_meta_box', 'Activity Details', [ $this, 'render_activity_meta_box' ], 'activity', 'normal', 'high'); }
    public function render_activity_meta_box( $post ) {
        wp_nonce_field( 'aura_save_activity_details', 'aura_activity_details_nonce' );
        
        $activity_date = get_post_meta( $post->ID, '_aura_activity_date', true );
        $activity_time = get_post_meta( $post->ID, '_aura_activity_time', true );
        $activity_code = get_post_meta( $post->ID, '_aura_activity_code', true );
        $document_ids = get_post_meta( $post->ID, '_aura_activity_document_ids', true );
        ?>
        <style>
            .document-item {
                padding: 8px;
                border: 1px solid #ddd;
                margin-bottom: 5px;
                background: #f9f9f9;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .document-item .dashicons-no-alt {
                color: #a00;
                cursor: pointer;
            }
        </style>
        <table class="form-table">
            <tbody>
                <tr>
                    <th><label for="aura_activity_date"><?php _e( 'Activity Date', 'aura-sync' ); ?></label></th>
                    <td><input type="text" id="aura_activity_date" name="aura_activity_date" value="<?php echo esc_attr( $activity_date ); ?>" class="regular-text" placeholder="Click to select date" autocomplete="off" /></td>
                </tr>
                <tr>
                    <th><label for="aura_activity_time"><?php _e( 'Activity Time', 'aura-sync' ); ?></label></th>
                    <td><input type="text" id="aura_activity_time" name="aura_activity_time" value="<?php echo esc_attr( $activity_time ); ?>" class="regular-text" placeholder="e.g., 9:00 AM" /></td>
                </tr>
                <tr>
                    <th><label for="aura_activity_code"><?php _e( 'Activity Code', 'aura-sync' ); ?></label></th>
                    <td><input type="text" value="<?php echo esc_attr( $activity_code ); ?>" class="regular-text" readonly /></td>
                </tr>
                <tr>
                    <th><?php _e( 'Documents', 'aura-sync' ); ?></th>
                    <td>
                        <div id="aura-documents-list">
                            <?php
                            if ( $document_ids ) {
                                $ids = explode(',', $document_ids);
                                foreach ($ids as $id) {
                                    if(empty(trim($id))) continue;
                                    $filename = basename( get_attached_file( $id ) );
                                    echo '<div class="document-item" data-id="' . esc_attr($id) . '">
                                        <span class="dashicons dashicons-media-default"></span>
                                        <span>' . esc_html($filename) . '</span>
                                        <a href="#" class="remove-document">
                                            <span class="dashicons dashicons-no-alt"></span>
                                        </a>
                                    </div>';
                                }
                            }
                            ?>
                        </div>
                        <input type="hidden" id="aura_activity_document_ids" name="aura_activity_document_ids" value="<?php echo esc_attr( $document_ids ); ?>">
                        <button type="button" id="add_aura_document" class="button"><?php _e( 'Add Document', 'aura-sync' ); ?></button>
                        <p class="description"><?php _e( 'Upload or select documents like tickets and waivers.', 'aura-sync' ); ?></p>
                    </td>
                </tr>
            </tbody>
        </table>
        <?php
    }

    /**
     * Save meta box data for activities
     */
    public function save_activity_meta_box_data( $post_id ) {
        // Check if this is an autosave
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }

        // Check if this is the right post type
        if ( get_post_type( $post_id ) !== 'activity' ) {
            return;
        }

        // Check nonce
        if ( ! isset( $_POST['aura_activity_details_nonce'] ) || 
             ! wp_verify_nonce( $_POST['aura_activity_details_nonce'], 'aura_save_activity_details' ) ) {
            return;
        }

        // Check user permissions
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Save the meta fields
        if ( empty( get_post_meta( $post_id, '_aura_activity_code', true ) ) ) {
            update_post_meta($post_id, '_aura_activity_code', 'AURA-' . $post_id);
        }
        if ( isset( $_POST['aura_activity_date'] ) ) {
            update_post_meta( $post_id, '_aura_activity_date', sanitize_text_field( $_POST['aura_activity_date'] ) );
        }

        if ( isset( $_POST['aura_activity_time'] ) ) {
            update_post_meta( $post_id, '_aura_activity_time', sanitize_text_field( $_POST['aura_activity_time'] ) );
        }

        if ( isset( $_POST['aura_activity_document_ids'] ) ) {
            update_post_meta( $post_id, '_aura_activity_document_ids', sanitize_text_field( $_POST['aura_activity_document_ids'] ) );
        }
    }

    /**
     * Inject Firebase configuration into the frontend
     */
    public function inject_firebase_config() {
        // Only inject on pages that need Firebase
        global $post;
        if ( is_a( $post, 'WP_Post' ) && 
             ( has_shortcode( $post->post_content, 'aura_activity_form' ) || 
               has_shortcode( $post->post_content, 'aura_operator_dashboard' ) ) ) {
            
            // Add Firebase config if needed for frontend operations
            echo "<!-- Firebase config would be injected here if needed -->\n";
        }
    }
}
new aura_Sync_Plugin();
