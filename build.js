const fs = require('fs');
const path = require('path');

// Simple build script for the Monoloci app
console.log('🔨 Building Monoloci App for deployment...');

// Create dist directory
const distDir = './dist';
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir);
}

// Files to copy to dist
const filesToCopy = [
    'index.html',
    'app.js',
    'style.css',
    'solo.css',
    'manifest.json',
    'sw.js',
    'journey.html',
    'journey.js'
    // Note: codex.js is generated directly in dist/ by the build:codex script
];

// Also copy the generated Tailwind CSS if it exists
if (fs.existsSync('./dist/tailwind.css')) {
    console.log('✅ Tailwind CSS already generated');
} else {
    console.log('⚠️  Run "npm run build:css-prod" first to generate Tailwind CSS');
}

// Copy files
filesToCopy.forEach(file => {
    if (fs.existsSync(file)) {
        fs.copyFileSync(file, path.join(distDir, file));
        console.log(`✅ Copied ${file}`);
    } else {
        console.log(`⚠️  Warning: ${file} not found`);
    }
});

// Create icons directory in dist
const iconsDir = path.join(distDir, 'icons');
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
}

// Copy actual icons from source icons directory
const sourceIconsDir = './icons';
if (fs.existsSync(sourceIconsDir)) {
    const iconFiles = fs.readdirSync(sourceIconsDir);
    iconFiles.forEach(iconFile => {
        if (iconFile.endsWith('.png') || iconFile.endsWith('.svg')) {
            const sourcePath = path.join(sourceIconsDir, iconFile);
            const destPath = path.join(iconsDir, iconFile);
            fs.copyFileSync(sourcePath, destPath);
            console.log(`✅ Copied icon: ${iconFile}`);
        }
    });

    // Create specific named icons for PWA compatibility
    if (fs.existsSync(path.join(sourceIconsDir, 'icon-192.png'))) {
        fs.copyFileSync(path.join(sourceIconsDir, 'icon-192.png'), path.join(iconsDir, 'icon-192.png'));
    }
    if (fs.existsSync(path.join(sourceIconsDir, 'icon-512.png'))) {
        fs.copyFileSync(path.join(sourceIconsDir, 'icon-512.png'), path.join(iconsDir, 'icon-512.png'));
    }

    console.log('✅ Copied actual icons from source');
} else {
    // Fallback: Create placeholder icons if source directory doesn't exist
    const iconSizes = [16, 32, 72, 96, 128, 144, 152, 192, 384, 512];
    iconSizes.forEach(size => {
        const iconContent = createPlaceholderIcon(size);
        fs.writeFileSync(path.join(iconsDir, `icon-${size}x${size}.png`), iconContent);
    });

    // Create apple-touch-icon
    fs.writeFileSync(path.join(iconsDir, 'apple-touch-icon.png'), createPlaceholderIcon(180));

    console.log('⚠️  Source icons not found, created placeholder icons');
}

// Create a simple deployment guide
const deploymentGuide = `
# Monoloci App - Deployment Guide

## 📦 Build Complete!

Your app has been built and is ready for deployment. The \`dist/\` folder contains all the files needed to host your app.

## 🚀 Deployment Options

### Option 1: Static Hosting Services

#### Netlify (Recommended)
1. Go to https://netlify.com
2. Drag and drop the \`dist/\` folder onto the deployment area
3. Your app will be live instantly with HTTPS

#### Vercel
1. Go to https://vercel.com
2. Import your project or drag the \`dist/\` folder
3. Deploy with one click

#### GitHub Pages
1. Push the \`dist/\` folder contents to a GitHub repository
2. Enable GitHub Pages in repository settings
3. Your app will be available at \`https://username.github.io/repository-name\`

#### Firebase Hosting
1. Install Firebase CLI: \`npm install -g firebase-tools\`
2. Run \`firebase init hosting\` in your project directory
3. Set public directory to \`dist\`
4. Run \`firebase deploy\`

### Option 2: Traditional Web Hosting
1. Upload the contents of the \`dist/\` folder to your web server
2. Ensure your server supports HTTPS (required for PWA features)
3. Configure your server to serve the manifest.json with correct MIME type

## 📱 Mobile App Creation

### Option 1: PWA (Progressive Web App) - Recommended
- Your app is already PWA-ready!
- Users can install it directly from their browser
- Works on both Android and iOS
- No app store approval needed

### Option 2: Capacitor (Native App Wrapper)
\`\`\`bash
# Install Capacitor
npm install @capacitor/core @capacitor/cli
npm install @capacitor/android @capacitor/ios

# Initialize Capacitor
npx cap init "Monoloci" "com.Monoloci.companion"

# Add platforms
npx cap add android
npx cap add ios

# Copy web assets
npx cap copy

# Open in native IDEs
npx cap open android
npx cap open ios
\`\`\`

### Option 3: Cordova
\`\`\`bash
# Install Cordova
npm install -g cordova

# Create Cordova project
cordova create Monoloci-mobile com.Monoloci.companion "Monoloci"
cd Monoloci-mobile

# Add platforms
cordova platform add android
cordova platform add ios

# Copy your dist files to www/ folder
# Then build
cordova build
\`\`\`

## 🔧 Configuration Before Deployment

### 1. Update Firebase Configuration
- Ensure your Firebase project is properly configured
- Update security rules for production
- Set up proper authentication

### 2. Update Domain References
- Replace localhost references with your actual domain
- Update any hardcoded URLs in the code

### 3. Icons and Branding
- Replace placeholder icons with your actual app icons
- Update app name and branding in manifest.json

### 4. Security
- Ensure HTTPS is enabled (required for PWA)
- Review and update Content Security Policy if needed
- Test all features in production environment

## 📊 Testing Your Deployment

1. **PWA Features**: Use Chrome DevTools > Application > Manifest
2. **Service Worker**: Check if it's registered and working
3. **Offline Functionality**: Test with network disabled
4. **Mobile Install**: Test "Add to Home Screen" on mobile devices
5. **Firebase Connection**: Verify data sync is working

## 🎯 Performance Optimization

- Enable gzip compression on your server
- Set up proper caching headers
- Consider using a CDN for static assets
- Optimize images and icons

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Verify Firebase configuration
3. Test PWA features using Chrome DevTools
4. Ensure HTTPS is properly configured

Your app is now ready for the world! 🌍
`;

fs.writeFileSync(path.join(distDir, 'DEPLOYMENT.md'), deploymentGuide);

console.log('📋 Created deployment guide');
console.log('🎉 Build complete! Check the dist/ folder for deployable files.');
console.log('📖 Read dist/DEPLOYMENT.md for deployment instructions.');

// Simple function to create placeholder icon (base64 encoded 1x1 PNG)
function createPlaceholderIcon(size) {
    // This is a minimal PNG file - you should replace with actual icons
    return Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
}
