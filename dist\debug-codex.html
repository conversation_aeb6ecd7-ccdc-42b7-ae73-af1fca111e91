<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Codex Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Codex Loading Debug</h1>
    
    <div id="test1" class="test info">
        <h3>Test 1: Check if dist/codex.js exists</h3>
        <p>Status: <span id="test1-status">Testing...</span></p>
        <pre id="test1-details"></pre>
    </div>

    <div id="test2" class="test info">
        <h3>Test 2: Check what's actually returned</h3>
        <p>Status: <span id="test2-status">Testing...</span></p>
        <pre id="test2-details"></pre>
    </div>

    <div id="test3" class="test info">
        <h3>Test 3: Try different paths</h3>
        <p>Status: <span id="test3-status">Testing...</span></p>
        <pre id="test3-details"></pre>
    </div>

    <script>
        async function runTests() {
            // Test 1: Basic fetch
            try {
                const response = await fetch('codex.js');
                const test1 = document.getElementById('test1');
                const status1 = document.getElementById('test1-status');
                const details1 = document.getElementById('test1-details');
                
                status1.textContent = `HTTP ${response.status} ${response.statusText}`;
                details1.textContent = `Content-Type: ${response.headers.get('content-type')}\nContent-Length: ${response.headers.get('content-length')}`;
                
                if (response.ok) {
                    test1.className = 'test success';
                } else {
                    test1.className = 'test error';
                }
            } catch (error) {
                document.getElementById('test1').className = 'test error';
                document.getElementById('test1-status').textContent = 'Failed';
                document.getElementById('test1-details').textContent = error.message;
            }

            // Test 2: Check content
            try {
                const response = await fetch('codex.js');
                const text = await response.text();
                const test2 = document.getElementById('test2');
                const status2 = document.getElementById('test2-status');
                const details2 = document.getElementById('test2-details');
                
                const firstChars = text.substring(0, 200);
                details2.textContent = `First 200 characters:\n${firstChars}`;
                
                if (text.startsWith('<!DOCTYPE') || text.startsWith('<html')) {
                    test2.className = 'test error';
                    status2.textContent = 'HTML returned instead of JavaScript!';
                } else if (text.startsWith('(() => {')) {
                    test2.className = 'test success';
                    status2.textContent = 'JavaScript content detected';
                } else {
                    test2.className = 'test error';
                    status2.textContent = 'Unexpected content';
                }
            } catch (error) {
                document.getElementById('test2').className = 'test error';
                document.getElementById('test2-status').textContent = 'Failed';
                document.getElementById('test2-details').textContent = error.message;
            }

            // Test 3: Try alternative paths
            const paths = ['/dist/codex.js', './dist/codex.js', 'codex.js'];
            let results = [];
            
            for (const path of paths) {
                try {
                    const response = await fetch(path);
                    results.push(`${path}: ${response.status} ${response.statusText}`);
                } catch (error) {
                    results.push(`${path}: ERROR - ${error.message}`);
                }
            }
            
            document.getElementById('test3-status').textContent = 'Complete';
            document.getElementById('test3-details').textContent = results.join('\n');
            document.getElementById('test3').className = 'test info';
        }

        // Run tests when page loads
        runTests();
    </script>
</body>
</html>
