Add a personal journey or a modern-day pilgrimage feature.


I will pre-select 8 sacred points or “resonant nodes” (Maybe a mix of known power sites + hidden harmonics you intuitively select.)
When a user visits one, they unlock a tone, glyph, or breath sequence.

(It could be local data–triggered, GPS-tied, or even honor-based: “I am here” → unlock.)
Each unlocked node adds a piece of a Codex Disk – a puzzle piece.

—visually assembling a mandala or activation wheel.
When they’ve unlocked 4 nodes, the app reveals:
    • a meditation + tone and half of the codex disc / glyph
When all 8 nodes are complete to app reveals:
    • A meditation based on the nature locations or events visited. – posible breath work.
    • The complete codex glyph (image download)
    • A custom Tone file they can download (eg. 428hz or Attonement mix of frequencies)
    • Or a reflection/phrase: “You’ve completed the West Spiral. Now breathe.”

Why This Works;
It gives people a reason to come back, travel intentionally, and feel
It allows the introduction of glyphs, breath work, and resonance philosophy without teaching it.

Simple Implementation Flow;
Pick 8 locations
Assign each a codex glyph piece + tone 
User “visits” → unlock
After X unlocks → give them something sacred
Reflection
Sound
Mini-guided breath
Codex phrase
Optional: Have a “My Spiral” screen where they watch their journey build via jigsaw type assembling.

-------



import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import * as Tone from 'tone';

// --- Helper Components & Icons ---

const LockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500">
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
    </svg>
);

const PlayIcon = () => <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg>;
const PauseIcon = () => <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"></path></svg>;

// --- Static Data ---
// UPDATED: Reduced to 8 nodes with new 8-sided clip-paths for the puzzle.
const initialSacredNodes = [
    { id: 1, name: "Uluru", tone: 'C4', unlocked: false, clipPath: 'polygon(50% 50%, 50% 0, 100% 0, 100% 50%)' },
    { id: 2, name: "Kata Tjuta", tone: 'D4', unlocked: false, clipPath: 'polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%)' },
    { id: 3, name: "Karlu Karlu", tone: 'E4', unlocked: false, clipPath: 'polygon(50% 50%, 50% 100%, 0 100%, 0 50%)' },
    { id: 4, name: "Wollumbin", tone: 'F4', unlocked: false, clipPath: 'polygon(50% 50%, 0 50%, 0 0, 50% 0)' },
    { id: 5, name: "The Pinnacles", tone: 'G4', unlocked: false, clipPath: 'polygon(50% 50%, 50% 0, 85.35% 14.65%, 100% 50%)' }, // Intermediate paths for visual flair
    { id: 6, name: "Ikara", tone: 'A4', unlocked: false, clipPath: 'polygon(50% 50%, 85.35% 85.35%, 50% 100%, 14.65% 85.35%)' },
    { id: 7, name: "Bay of Fires", tone: 'B4', unlocked: false, clipPath: 'polygon(50% 50%, 14.65% 14.65%, 0 50%, 14.65% 85.35%)' },
    { id: 8, name: "Three Sisters", tone: 'C5', unlocked: false, clipPath: 'polygon(50% 50%, 85.35% 14.65%, 50% 0, 14.65% 14.65%)' },
];

// UPDATED: Rewards structure for 8 nodes.
const rewards = {
    4: {
        title: "The First Resonance",
        message: "You have connected four sacred nodes, opening a channel of harmonic energy. Listen to the Meditation of Awakening to integrate the frequency.",
        audioSrc: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3", // Replace with your actual audio file
        downloadSrc: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3" // Replace with your file
    },
    8: {
        title: "The Codex is Complete!",
        message: "You have walked the path and unified the sacred spiral. The full resonance of the land is now open to you. This is your Aetheria Codex.",
        imageSrc: "https://placehold.co/400x400/022c43/9af1ff?text=Your+Codex", // Replace with your final codex image
        audioSrc: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3", // Replace with your final meditation
        downloadSrc: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3",
        voucherLink: "#" // Replace with a link to your voucher page/logic
    }
};

// --- Modal Components (No changes needed here) ---

function NodeDetailModal({ node, onUnlock, onClose }) {
    return (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <motion.div initial={{ scale: 0.9, y: 20 }} animate={{ scale: 1, y: 0 }} exit={{ scale: 0.9, y: 20 }} className="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 w-full max-w-sm p-6 text-center" onClick={(e) => e.stopPropagation()}>
                <h2 className="text-2xl font-bold text-cyan-300">{node.name}</h2>
                <p className="text-gray-400 text-sm mt-1">Resonant Node {node.id}</p>
                <div className="my-6 p-4 bg-gray-900/50 rounded-lg flex items-center justify-center min-h-[100px]">
                    {node.unlocked ? <p className="text-cyan-400 text-lg">UNLOCKED</p> : <p className="text-gray-400">Visit this site to reveal its piece of the codex.</p>}
                </div>
                {!node.unlocked && <button onClick={() => onUnlock(node)} className="w-full bg-cyan-500 text-gray-900 font-bold py-3 px-6 rounded-lg hover:bg-cyan-400 transition-colors duration-300 transform hover:scale-105">I Am Here - Unlock Node</button>}
                <button onClick={onClose} className="w-full mt-3 bg-gray-700 text-gray-300 font-medium py-2 px-6 rounded-lg hover:bg-gray-600 transition-colors duration-300">Close</button>
            </motion.div>
        </motion.div>
    );
}

function RewardModal({ reward, onClose }) {
    const audioRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);

    const togglePlayPause = () => {
        if (audioRef.current) {
            if (isPlaying) {
                audioRef.current.pause();
            } else {
                audioRef.current.play();
            }
            setIsPlaying(!isPlaying);
        }
    };
    
    useEffect(() => {
        const audioEl = audioRef.current;
        const handleEnded = () => setIsPlaying(false);
        if (audioEl) {
            audioEl.addEventListener('ended', handleEnded);
        }
        return () => {
            if (audioEl) {
                audioEl.removeEventListener('ended', handleEnded);
            }
        }
    }, []);

    return (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <motion.div initial={{ scale: 0.9, y: 20 }} animate={{ scale: 1, y: 0 }} exit={{ scale: 0.9, y: 20 }} className="bg-gradient-to-br from-cyan-900 to-gray-900 rounded-2xl shadow-2xl border border-cyan-500/50 w-full max-w-md p-8 text-center" onClick={(e) => e.stopPropagation()}>
                {reward.imageSrc && <img src={reward.imageSrc} alt="Codex Revealed" className="w-48 h-48 mx-auto rounded-lg mb-6 shadow-lg" />}
                <h2 className="text-3xl font-bold text-cyan-300">{reward.title}</h2>
                <p className="text-gray-300 mt-4 mb-8 text-lg">{reward.message}</p>
                
                {reward.audioSrc && (
                    <div className="flex items-center justify-center space-x-4 bg-black/20 p-3 rounded-lg mb-6">
                        <audio ref={audioRef} src={reward.audioSrc} onPlay={() => setIsPlaying(true)} onPause={() => setIsPlaying(false)}></audio>
                        <button onClick={togglePlayPause} className="text-cyan-300 hover:text-white transition-colors">
                            {isPlaying ? <PauseIcon /> : <PlayIcon />}
                        </button>
                        <p className="text-gray-300 font-medium">Play Meditation</p>
                    </div>
                )}
                
                {reward.downloadSrc && <a href={reward.downloadSrc} download className="block w-full mb-3 bg-cyan-500 text-gray-900 font-bold py-3 px-8 rounded-lg hover:bg-cyan-400 transition-colors duration-300">Download Reward</a>}
                {reward.voucherLink && <a href={reward.voucherLink} className="block w-full bg-yellow-500 text-gray-900 font-bold py-3 px-8 rounded-lg hover:bg-yellow-400 transition-colors duration-300">Claim Voucher</a>}
                
                <button onClick={onClose} className="w-full mt-4 bg-gray-700 text-gray-300 font-medium py-2 px-6 rounded-lg hover:bg-gray-600 transition-colors duration-300">Continue Journey</button>
            </motion.div>
        </motion.div>
    );
}

function RevealModal({ node }) {
    if (!node) return null;

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 flex flex-col items-center justify-center z-50 p-4 pointer-events-none"
        >
            <motion.div
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.5, opacity: 0 }}
                transition={{ type: 'spring', damping: 15, stiffness: 200 }}
                className="relative"
            >
                <img
                    src="https://placehold.co/400x400/022c43/9af1ff?text=Codex" // The full codex image
                    alt="Revealed Piece"
                    className="w-[min(80vw,400px)] h-[min(80vw,400px)]"
                    style={{ clipPath: node.clipPath }}
                />
            </motion.div>
            <motion.p 
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-white text-center text-xl font-bold mt-4 bg-black/50 rounded-lg px-4 py-2">
                {node.name} Unlocked!
            </motion.p>
        </motion.div>
    );
}

// --- Main App Component ---

export default function App() {
    const [nodes, setNodes] = useState([]);
    const [selectedNode, setSelectedNode] = useState(null);
    const [reward, setReward] = useState(null);
    const [revealedNode, setRevealedNode] = useState(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const synth = useMemo(() => {
        if (typeof window !== 'undefined') {
            return new Tone.MembraneSynth().toDestination();
        }
        return null;
    }, []);

    useEffect(() => {
        // More robust initialization
        setNodes(initialSacredNodes);
        setIsInitialized(true); 
    }, []);

    const unlockedCount = useMemo(() => {
        if (!nodes) return 0;
        return nodes.filter(n => n.unlocked).length;
    }, [nodes]);

    useEffect(() => {
        if (rewards[unlockedCount] && unlockedCount > 0) {
            setTimeout(() => {
                setReward(rewards[unlockedCount]);
            }, 2600); // Delay reward modal to show after reveal modal
        }
    }, [unlockedCount]);

    const handleUnlockNode = useCallback((nodeId) => {
        const nodeToUnlock = nodes.find(n => n.id === nodeId);
        if (synth && nodeToUnlock && Tone.context.state === 'running') {
            synth.triggerAttackRelease(nodeToUnlock.tone, "8n", Tone.now());
        }
        const updatedNodes = nodes.map(n => n.id === nodeId ? { ...n, unlocked: true } : n);
        setNodes(updatedNodes);
        setSelectedNode(null);

        setRevealedNode(nodeToUnlock);
        setTimeout(() => {
            setRevealedNode(null);
        }, 2500); 

    }, [nodes, synth]);

    const handleHonorUnlock = useCallback((node) => {
        if (Tone.context.state !== 'running') {
            Tone.context.resume().then(() => handleUnlockNode(node.id));
        } else {
            handleUnlockNode(node.id);
        }
    }, [handleUnlockNode]);
    
    const codexSize = "min(80vw, 400px)";
    
    // Render nothing until initialized to prevent blank screen errors
    if (!isInitialized) {
        return <div className="bg-gray-900 min-h-screen" />;
    }

    return (
        <div className="bg-gray-900 text-gray-100 min-h-screen font-sans flex flex-col items-center justify-center p-4 overflow-hidden">
            <div className="text-center mb-8">
                <h1 className="text-4xl md:text-5xl font-bold text-cyan-300 tracking-wider">Aetheria Codex</h1>
                <p className="text-gray-400 mt-2">{unlockedCount === (nodes?.length || 0) && nodes.length > 0 ? "The spiral is complete." : "Reveal the sacred pattern."}</p>
            </div>

            <div className="relative flex items-center justify-center" style={{ width: codexSize, height: codexSize }}>
                <img 
                    src="https://placehold.co/400x400/022c43/9af1ff?text=Codex"
                    alt="Codex background" 
                    className="absolute inset-0 w-full h-full rounded-full opacity-20"
                />
                
                {nodes.map((node) => (
                    <div key={node.id} className="absolute inset-0 w-full h-full">
                        <AnimatePresence>
                            {node.unlocked && (
                                <motion.div
                                    className="absolute inset-0 w-full h-full"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 1.5 }}
                                >
                                    <img 
                                        src="https://placehold.co/400x400/022c43/9af1ff?text=Codex"
                                        alt={`Piece ${node.id}`}
                                        className="w-full h-full"
                                        style={{ clipPath: node.clipPath }}
                                    />
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                ))}

                {/* UPDATED: Node positioning logic for 8 nodes */}
                {nodes.map((node, index) => {
                    const angle = (index / nodes.length) * (2 * Math.PI) - (Math.PI / 2);
                    const x = `calc(50% + ${Math.cos(angle) * 45}% - 16px)`;
                    const y = `calc(50% + ${Math.sin(angle) * 45}% - 16px)`;
                    return (
                        <motion.button
                            key={`button-${node.id}`}
                            onClick={() => setSelectedNode(node)}
                            className="absolute w-8 h-8 rounded-full flex items-center justify-center transition-all duration-500"
                            style={{ top: y, left: x }}
                            whileHover={{ scale: 1.2 }}
                            animate={{
                                backgroundColor: node.unlocked ? 'rgba(0, 255, 255, 0.3)' : 'rgba(55, 65, 81, 0.7)',
                                border: node.unlocked ? '1px solid rgba(0, 255, 255, 0.7)' : '1px solid rgba(107, 114, 128, 0.5)',
                            }}
                        >
                            {!node.unlocked && <LockIcon />}
                        </motion.button>
                    );
                })}
            </div>
            
            <div className="text-center mt-8 max-w-md">
                <p className="text-gray-400">
                    {unlockedCount} of {nodes?.length || 0} nodes unlocked. Tap a locked node to learn more.
                </p>
            </div>

            <AnimatePresence>
                {selectedNode && <NodeDetailModal node={selectedNode} onUnlock={handleHonorUnlock} onClose={() => setSelectedNode(null)} />}
                {reward && <RewardModal reward={reward} onClose={() => setReward(null)} />}
                {revealedNode && <RevealModal node={revealedNode} />}
            </AnimatePresence>
        </div>
    );
}
