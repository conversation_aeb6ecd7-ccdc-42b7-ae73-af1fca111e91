{"name": "Monoloci-activity-companion", "version": "1.0.0", "description": "Monoloci: Your Activity Companion - A safety-focused mobile app for activity participants", "main": "index.html", "scripts": {"dev": "python start-server.py", "build:css": "npx tailwindcss -i ./src/input.css -o ./dist/tailwind.css --watch", "build:css-prod": "npx tailwindcss -i ./src/input.css -o ./dist/tailwind.css --minify", "build:codex": "esbuild src/features/codex/index.jsx --bundle --outfile=dist/codex.js --jsx-factory=React.createElement --jsx-fragment=React.Fragment", "build": "npm run build:codex && node build.js", "deploy": "npm run build && echo 'Build complete! Deploy the dist/ folder to your hosting service'", "serve": "python -m http.server 8000", "test": "echo 'Open firebase-test.html to test Firebase connectivity'"}, "keywords": ["activity", "safety", "mobile", "pwa", "firebase", "emergency"], "author": "Monoloci Development Team", "license": "GPL-2.0", "dependencies": {"firebase": "^10.12.2", "framer-motion": "^11.2.12", "react": "^18.3.1", "react-dom": "^18.3.1", "tone": "^15.0.4"}, "devDependencies": {"clean-css": "^5.3.2", "esbuild": "^0.21.5", "tailwindcss": "^4.1.11", "terser": "^5.19.4"}, "repository": {"type": "git", "url": "https://github.com/yourusername/Monoloci-app.git"}, "homepage": "https://Monoloci-app-backend.web.app/", "engines": {"node": ">=14.0.0"}}