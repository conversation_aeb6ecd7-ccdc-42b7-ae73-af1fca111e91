import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import * as Tone from 'tone';
import { getFirebase } from './firebase';
import { collection, getDocs, doc, getDoc, updateDoc, arrayUnion } from 'firebase/firestore';
import { onAuthStateChanged } from 'firebase/auth';

// --- Helper Components & Icons ---

const LockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
    </svg>
);

// --- Static Data ---
// This is now fetched from Firebase
// const initialSacredNodes = [ ... ];

// --- Modal Components ---

function NodeDetailModal({ node, onUnlock, onClose }) {
    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
            onClick={onClose}
        >
            <motion.div
                initial={{ scale: 0.9, y: 20 }}
                animate={{ scale: 1, y: 0 }}
                exit={{ scale: 0.9, y: 20 }}
                className="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 w-full max-w-sm p-6 text-center"
                onClick={(e) => e.stopPropagation()}
            >
                <h2 className="text-2xl font-bold text-cyan-300">{node.name}</h2>
                <p className="text-gray-400 text-sm mt-1">Resonant Node</p>

                <div className="my-6 p-4 bg-gray-900/50 rounded-lg flex items-center justify-center min-h-[120px]">
                    {node.unlocked ? (
                         <div className="flex flex-col items-center">
                            <svg viewBox="0 0 24 24" className="w-20 h-20 text-cyan-300">
                                <path d={node.glyph} stroke="currentColor" strokeWidth="1" fill="none" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                             <p className="text-cyan-400 mt-4 text-lg">UNLOCKED</p>
                         </div>
                    ) : (
                        <div className="flex flex-col items-center">
                            <LockIcon />
                            <p className="text-gray-400 mt-2">Visit this site to unlock its glyph.</p>
                        </div>
                    )}
                </div>

                {!node.unlocked && (
                    <button
                        onClick={() => onUnlock(node)}
                        className="w-full bg-cyan-500 text-gray-900 font-bold py-3 px-6 rounded-lg hover:bg-cyan-400 transition-colors duration-300 transform hover:scale-105"
                    >
                        I Am Here - Unlock Node
                    </button>
                )}

                <button
                    onClick={onClose}
                    className="w-full mt-3 bg-gray-700 text-gray-300 font-medium py-2 px-6 rounded-lg hover:bg-gray-600 transition-colors duration-300"
                >
                    Close
                </button>
            </motion.div>
        </motion.div>
    );
}

function RewardModal({ reward, onClose }) {
    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={onClose}
        >
            <motion.div
                initial={{ scale: 0.9, y: 20 }}
                animate={{ scale: 1, y: 0 }}
                exit={{ scale: 0.9, y: 20 }}
                className="bg-gradient-to-br from-cyan-900 to-gray-900 rounded-2xl shadow-2xl border border-cyan-500/50 w-full max-w-md p-8 text-center"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-center mb-4">
                     <svg viewBox="0 0 24 24" className="w-16 h-16 text-cyan-300 animate-pulse">
                        <path d={reward.glyph} stroke="currentColor" strokeWidth="1" fill="none" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                </div>
                <h2 className="text-3xl font-bold text-cyan-300">{reward.title}</h2>
                <p className="text-gray-300 mt-4 mb-8 text-lg">{reward.message}</p>
                <button
                    onClick={onClose}
                    className="bg-cyan-500 text-gray-900 font-bold py-3 px-8 rounded-lg hover:bg-cyan-400 transition-colors duration-300 transform hover:scale-105"
                >
                    Continue Journey
                </button>
            </motion.div>
        </motion.div>
    );
}


// --- Main App Component ---

export default function Codex() {
    const [nodes, setNodes] = useState([]);
    const [selectedNode, setSelectedNode] = useState(null);
    const [reward, setReward] = useState(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const [currentUser, setCurrentUser] = useState(null);
    const synth = useMemo(() => {
        if (typeof window !== 'undefined') {
            return new Tone.MembraneSynth().toDestination();
        }
        return null;
    }, []);

    useEffect(() => {
        const codexId = "codex"; // This can be made dynamic later
        let unsubscribe = null;

        // Wait for Firebase to be ready before setting up auth listener
        getFirebase()
            .then(({ auth }) => {
                console.log("Codex: Setting up auth listener");
                unsubscribe = onAuthStateChanged(auth, (user) => {
                    if (user) {
                        setCurrentUser(user);
                        fetchCodexData(codexId, user.uid);
                    } else {
                        // Handle user not signed in
                        console.log("User is not signed in.");
                        setIsInitialized(true); // Stop loading if no user
                    }
                });
            })
            .catch((error) => {
                console.error("Codex: Failed to initialize Firebase auth:", error);
                setIsInitialized(true); // Stop loading on error
            });

        return () => {
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, []);

    const fetchCodexData = async (codexId, userId) => {
        try {
            // Wait for Firebase to be ready
            const { db } = await getFirebase();

            // 1. Fetch all nodes for the given codex
            const nodesCollectionRef = collection(db, `codexes/${codexId}/nodes`);
            const nodesSnapshot = await getDocs(nodesCollectionRef);
            const allNodes = nodesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

            // 2. Fetch the user's unlocked nodes
            const userDocRef = doc(db, `users/${userId}`);
            const userDocSnap = await getDoc(userDocRef);
            const unlockedNodeIds = userDocSnap.exists() ? userDocSnap.data().unlocked_nodes || [] : [];

            // 3. Merge the data
            const mergedNodes = allNodes.map(node => ({
                ...node,
                unlocked: unlockedNodeIds.includes(node.id)
            }));

            setNodes(mergedNodes);
        } catch (error) {
            console.error("Error fetching codex data:", error);
        } finally {
            setIsInitialized(true);
        }
    };

    const unlockedCount = useMemo(() => nodes.filter(n => n.unlocked).length, [nodes]);

    const handleUnlockNode = useCallback(async (nodeId) => {
        if (!currentUser) {
            console.error("Cannot unlock: No user is signed in.");
            return;
        }

        const nodeToUnlock = nodes.find(n => n.id === nodeId);
        if (synth && nodeToUnlock && Tone.context.state === 'running') {
            synth.triggerAttackRelease(nodeToUnlock.tone, "8n", Tone.now());
        }

        // Update Firestore
        try {
            const { db } = await getFirebase();
            const userDocRef = doc(db, `users/${currentUser.uid}`);
            await updateDoc(userDocRef, {
                unlocked_nodes: arrayUnion(nodeId)
            });
            console.log(`Node ${nodeId} unlocked for user ${currentUser.uid}`);

            // Update local state for immediate UI feedback
            const updatedNodes = nodes.map(n =>
                n.id === nodeId ? { ...n, unlocked: true } : n
            );
            setNodes(updatedNodes);
            setSelectedNode(null);

            const newUnlockedCount = updatedNodes.filter(n => n.unlocked).length;
            if (newUnlockedCount === 5) {
                setReward({
                    title: "The First Spiral Complete",
                    message: "You have awakened the initial harmonic sequence. Take a moment to feel the resonance of the land. A new meditation is now available.",
                    glyph: "M12 2a10 10 0 100 20 10 10 0 000-20zm-1 5v5l-4 2"
                });
            }
        } catch (error) {
            console.error("Error updating user document:", error);
            // If the user document doesn't exist, you might want to create it first.
            // For this example, we assume the user doc exists.
        }
    }, [nodes, synth, currentUser]);

    const handleHonorUnlock = useCallback((node) => {
        // Resume audio context on user interaction, which is required by modern browsers.
        if (Tone.context.state !== 'running') {
            Tone.context.resume().then(() => {
                handleUnlockNode(node.id);
            });
        } else {
            handleUnlockNode(node.id);
        }
    }, [handleUnlockNode]);
    
    const codexRadius = "min(40vw, 200px)";

    if (!isInitialized) {
        // Render a simple loading state or nothing to avoid errors before hydration
        return <div className="bg-gray-900 min-h-screen"></div>;
    }

    return (
        <div className="bg-gray-900 text-gray-100 min-h-screen font-sans flex flex-col items-center justify-center p-4 overflow-hidden">
            <div className="text-center mb-8">
                <h1 className="text-4xl md:text-5xl font-bold text-cyan-300 tracking-wider">Aetheria Codex</h1>
                <p className="text-gray-400 mt-2">Tap a node to begin your journey.</p>
            </div>

            <div className="relative w-[calc(2*var(--radius))] h-[calc(2*var(--radius))] flex items-center justify-center" style={{'--radius': codexRadius}}>
                <div className="absolute w-32 h-32 bg-gray-800 rounded-full flex flex-col items-center justify-center shadow-2xl z-10 border-2 border-cyan-500/30">
                    <span className="text-3xl font-bold text-white">{unlockedCount}</span>
                    <span className="text-sm text-gray-400">/ {nodes.length}</span>
                    <span className="text-xs text-cyan-400 tracking-widest mt-1">UNLOCKED</span>
                </div>

                {nodes.map((node, index) => {
                    const angle = (index / nodes.length) * 360;
                    return (
                        <motion.div
                            key={node.id}
                            className="absolute top-1/2 left-1/2 w-16 h-16 -m-8"
                            style={{ transform: `rotate(${angle}deg) translateX(var(--radius))` }}
                        >
                            <motion.button
                                onClick={() => setSelectedNode(node)}
                                className="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-500"
                                style={{ transform: `rotate(${-angle}deg)` }}
                                whileHover={{ scale: 1.1 }}
                                animate={{
                                    backgroundColor: node.unlocked ? 'rgba(0, 255, 255, 0.2)' : 'rgba(55, 65, 81, 0.5)',
                                    boxShadow: node.unlocked ? '0 0 15px rgba(0, 255, 255, 0.5)' : 'none',
                                    border: node.unlocked ? '2px solid rgba(0, 255, 255, 0.7)' : '2px solid rgba(107, 114, 128, 0.3)',
                                }}
                            >
                                {node.unlocked ? (
                                    <svg viewBox="0 0 24 24" className="w-8 h-8 text-cyan-300">
                                        <path d={node.glyph} stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                ) : (
                                    <LockIcon />
                                )}
                            </motion.button>
                        </motion.div>
                    );
                })}
            </div>
            
             <div className="text-center mt-12 max-w-md">
                <p className="text-gray-400">
                    As you visit each sacred site, a new glyph will be revealed, and a harmonic tone will sound, attuning you to the Earth's resonance.
                </p>
            </div>

            <AnimatePresence>
                {selectedNode && (
                    <NodeDetailModal node={selectedNode} onUnlock={handleHonorUnlock} onClose={() => setSelectedNode(null)} />
                )}
                {reward && (
                    <RewardModal reward={reward} onClose={() => setReward(null)} />
                )}
            </AnimatePresence>
        </div>
    );
}