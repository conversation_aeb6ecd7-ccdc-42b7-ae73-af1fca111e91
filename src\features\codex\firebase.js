// This script now retrieves the globally shared Firebase instance
// initialized by the main app.js file. This prevents conflicts
// from re-initializing the Firebase app.

// Create a promise that resolves when Firebase is ready
let firebaseReady = false;
let firebasePromise = null;

function waitForFirebase() {
  if (firebasePromise) {
    return firebasePromise;
  }

  firebasePromise = new Promise((resolve, reject) => {
    // Check if already available
    if (window.auraFirebase?.db && window.auraFirebase?.auth) {
      firebaseReady = true;
      console.log("Codex: Firebase already available");
      resolve({
        db: window.auraFirebase.db,
        auth: window.auraFirebase.auth
      });
      return;
    }

    console.log("Codex: Waiting for Firebase to be initialized...");

    let retryCount = 0;
    const maxRetries = 100; // 10 seconds max wait

    const checkFirebase = () => {
      retryCount++;

      if (window.auraFirebase?.db && window.auraFirebase?.auth) {
        firebaseReady = true;
        console.log("Codex: Firebase initialized successfully");
        resolve({
          db: window.auraFirebase.db,
          auth: window.auraFirebase.auth
        });
      } else if (retryCount >= maxRetries) {
        console.error("FATAL: Firebase was not initialized after 10 seconds");
        reject(new Error("Firebase initialization timeout"));
      } else {
        setTimeout(checkFirebase, 100);
      }
    };

    checkFirebase();
  });

  return firebasePromise;
}

// Export the promise-based Firebase access
export const getFirebase = waitForFirebase;

// Legacy exports for backward compatibility (but these should not be used directly)
export let db = null;
export let auth = null;

// Initialize legacy exports when Firebase is ready
waitForFirebase().then(({ db: firebaseDb, auth: firebaseAuth }) => {
  db = firebaseDb;
  auth = firebaseAuth;
}).catch(error => {
  console.error("Failed to initialize Firebase:", error);
});