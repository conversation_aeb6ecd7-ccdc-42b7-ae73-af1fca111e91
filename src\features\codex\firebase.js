// This script now retrieves the globally shared Firebase instance
// initialized by the main app.js file. This prevents conflicts
// from re-initializing the Firebase app.

let db, auth;

// Wait for Firebase to be available
function initializeFirebaseRefs() {
  if (window.auraFirebase?.db && window.auraFirebase?.auth) {
    db = window.auraFirebase.db;
    auth = window.auraFirebase.auth;
    console.log("Codex: Firebase references initialized successfully");
    return true;
  }
  return false;
}

// Try to initialize immediately
if (!initializeFirebaseRefs()) {
  console.warn("Codex: Firebase not yet available, will retry...");

  // If not available, wait and retry
  let retryCount = 0;
  const maxRetries = 50; // 5 seconds max wait

  const waitForFirebase = setInterval(() => {
    retryCount++;

    if (initializeFirebaseRefs()) {
      clearInterval(waitForFirebase);
    } else if (retryCount >= maxRetries) {
      clearInterval(waitForFirebase);
      console.error("FATAL: Firebase was not initialized by the main app after 5 seconds. The Codex feature cannot function.");

      // Show user-facing error
      const codexRoot = document.getElementById('codex-root');
      if (codexRoot) {
        codexRoot.innerHTML = `
          <div class="flex items-center justify-center min-h-[400px] text-center p-8">
            <div>
              <h2 class="text-xl font-bold text-red-600 mb-4">Codex Loading Error</h2>
              <p class="text-gray-600">Firebase connection failed. Please refresh the page and try again.</p>
            </div>
          </div>
        `;
      }
    }
  }, 100);
}

export { db, auth };